'use client';

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);

  const menuItems = [
    { key: '/dashboard', label: '仪表板', path: '/dashboard' },
    { key: '/users', label: '用户管理', path: '/users' },
    { key: '/products', label: '产品管理', path: '/products' },
    { key: '/settings', label: '设置', path: '/settings' },
  ];

  // 面包屑导航
  const getBreadcrumbs = () => {
    const currentItem = menuItems.find(item => item.path === pathname);
    if (pathname === '/dashboard') {
      return [{ title: '仪表板', path: '/dashboard' }];
    }
    return [
      { title: '仪表板', path: '/dashboard' },
      ...(currentItem ? [{ title: currentItem.label, path: currentItem.path }] : [])
    ];
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/" className="text-xl font-bold text-blue-600">
              管理系统
            </Link>
            <nav className="hidden md:flex space-x-1">
              {getBreadcrumbs().map((crumb, index) => (
                <div key={crumb.path} className="flex items-center">
                  {index > 0 && <span className="mx-2 text-gray-400">/</span>}
                  <Link
                    href={crumb.path}
                    className={`px-2 py-1 rounded text-sm ${
                      crumb.path === pathname
                        ? 'text-blue-600 font-medium'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {crumb.title}
                  </Link>
                </div>
              ))}
            </nav>
          </div>
          
          <div className="flex items-center space-x-4">
            <button className="p-2 text-gray-400 hover:text-gray-600">
              🔔
            </button>
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
              U
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* 侧边栏 */}
        <aside className={`bg-white shadow-sm transition-all duration-300 ${
          collapsed ? 'w-16' : 'w-64'
        } sticky top-16 h-[calc(100vh-4rem)]`}>
          <div className="p-4">
            {/* 折叠按钮 */}
            <button
              onClick={() => setCollapsed(!collapsed)}
              className="w-full mb-4 p-2 rounded-md hover:bg-gray-100 transition-colors flex items-center justify-center"
              title={collapsed ? '展开菜单' : '折叠菜单'}
            >
              {collapsed ? '→' : '←'}
            </button>
            
            {/* 菜单项 */}
            <nav className="space-y-2">
              {menuItems.map((item) => (
                <Link
                  key={item.key}
                  href={item.path}
                  className={`block px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    pathname === item.path
                      ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                  title={collapsed ? item.label : undefined}
                >
                  <div className="flex items-center">
                    <span className="w-5 h-5 flex items-center justify-center mr-3">
                      {item.key === '/dashboard' && '📊'}
                      {item.key === '/users' && '👥'}
                      {item.key === '/products' && '📦'}
                      {item.key === '/settings' && '⚙️'}
                    </span>
                    {!collapsed && <span>{item.label}</span>}
                  </div>
                </Link>
              ))}
            </nav>
          </div>
        </aside>

        {/* 主内容区域 */}
        <main className="flex-1 overflow-auto">
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
