'use client';

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

export default function Home() {
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);

  const menuItems = [
    { key: '/dashboard', label: '仪表板', path: '/dashboard' },
    { key: '/users', label: '用户管理', path: '/users' },
    { key: '/products', label: '产品管理', path: '/products' },
    { key: '/settings', label: '设置', path: '/settings' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="px-6 py-4">
          <h1 className="text-xl font-semibold text-gray-800">管理系统</h1>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className={`bg-white shadow-sm transition-all duration-300 ${collapsed ? 'w-16' : 'w-64'}`}>
          <div className="p-4">
            <button
              onClick={() => setCollapsed(!collapsed)}
              className="mb-4 p-2 rounded-md hover:bg-gray-100 transition-colors"
            >
              {collapsed ? '→' : '←'}
            </button>

            <nav className="space-y-2">
              {menuItems.map((item) => (
                <Link
                  key={item.key}
                  href={item.path}
                  className={`block px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    pathname === item.path
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  {collapsed ? item.label.charAt(0) : item.label}
                </Link>
              ))}
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">欢迎来到首页</h2>
            <p className="text-gray-600 mb-6">
              这是一个带有嵌套路由的 Next.js 应用示例。点击左侧菜单可以导航到不同的页面。
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {menuItems.map((item) => (
                <Link
                  key={item.key}
                  href={item.path}
                  className="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all"
                >
                  <h3 className="font-medium text-gray-900 mb-2">{item.label}</h3>
                  <p className="text-sm text-gray-500">点击进入 {item.label} 页面</p>
                </Link>
              ))}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
